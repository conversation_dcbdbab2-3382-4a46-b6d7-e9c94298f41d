/**
 * 服务器端口管理相关常量
 *
 * <AUTHOR>
 * @Date 2025-07-27
 */

/**
 * 端口协议枚举
 */
export const PORT_PROTOCOL_ENUM = {
  TCP: {
    value: 'TCP',
    desc: 'TCP协议',
  },
  UDP: {
    value: 'UDP',
    desc: 'UDP协议',
  },
};

/**
 * 互联网访问级别枚举
 */
export const INTERNET_ACCESS_LEVEL_ENUM = {
  NO_ACCESS: {
    value: 0,
    desc: '不开放',
    color: 'default',
  },
  OUTBOUND_ONLY: {
    value: 1,
    desc: '出网访问',
    color: 'blue',
  },
  INBOUND_ONLY: {
    value: 2,
    desc: '入网访问',
    color: 'orange',
  },
  BIDIRECTIONAL: {
    value: 3,
    desc: '双向访问',
    color: 'green',
  },
};

/**
 * 访问控制类型枚举
 */
export const ACCESS_CONTROL_TYPE_ENUM = {
  OPEN_ALL: {
    value: 0,
    desc: '全开放',
    color: 'green',
  },
  POINT_TO_POINT: {
    value: 1,
    desc: '点对点访问',
    color: 'orange',
  },
};

/**
 * 端口状态枚举
 */
export const PORT_STATUS_ENUM = {
  PENDING: {
    value: 0,
    desc: '待确认',
    color: 'orange',
  },
  CONFIRMED: {
    value: 1,
    desc: '已确认',
    color: 'green',
  },
};

/**
 * 常用端口预设
 */
export const COMMON_PORTS = [
  { port: 22, service: 'SSH', purpose: 'SSH远程连接', protocol: 'TCP' },
  { port: 80, service: 'HTTP', purpose: 'Web服务', protocol: 'TCP' },
  { port: 443, service: 'HTTPS', purpose: 'HTTPS服务', protocol: 'TCP' },
  { port: 21, service: 'FTP', purpose: 'FTP文件传输', protocol: 'TCP' },
  { port: 23, service: 'Telnet', purpose: 'Telnet远程连接', protocol: 'TCP' },
  { port: 25, service: 'SMTP', purpose: '邮件发送', protocol: 'TCP' },
  { port: 53, service: 'DNS', purpose: 'DNS解析', protocol: 'UDP' },
  { port: 110, service: 'POP3', purpose: '邮件接收', protocol: 'TCP' },
  { port: 143, service: 'IMAP', purpose: '邮件访问', protocol: 'TCP' },
  { port: 993, service: 'IMAPS', purpose: '安全邮件访问', protocol: 'TCP' },
  { port: 995, service: 'POP3S', purpose: '安全邮件接收', protocol: 'TCP' },
  { port: 3306, service: 'MySQL', purpose: 'MySQL数据库', protocol: 'TCP' },
  { port: 5432, service: 'PostgreSQL', purpose: 'PostgreSQL数据库', protocol: 'TCP' },
  { port: 1433, service: 'SQL Server', purpose: 'SQL Server数据库', protocol: 'TCP' },
  { port: 1521, service: 'Oracle', purpose: 'Oracle数据库', protocol: 'TCP' },
  { port: 6379, service: 'Redis', purpose: 'Redis缓存', protocol: 'TCP' },
  { port: 27017, service: 'MongoDB', purpose: 'MongoDB数据库', protocol: 'TCP' },
  { port: 8080, service: 'Tomcat', purpose: 'Tomcat应用服务器', protocol: 'TCP' },
  { port: 8443, service: 'HTTPS-Alt', purpose: '备用HTTPS服务', protocol: 'TCP' },
  { port: 9200, service: 'Elasticsearch', purpose: 'Elasticsearch搜索', protocol: 'TCP' },
];