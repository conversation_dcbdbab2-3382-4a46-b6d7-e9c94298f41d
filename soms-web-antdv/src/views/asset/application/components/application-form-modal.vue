<template>
  <a-modal :title="modalTitle" v-model:open="visible" :width="800" :maskClosable="false" :keyboard="false" @ok="handleOk" @cancel="handleCancel">
    <a-form ref="formRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-item label="系统名称" name="name">
        <a-input v-model:value="formModel.name" placeholder="请输入系统名称" />
      </a-form-item>

      <a-form-item label="系统类型" name="type">
        <a-select v-model:value="formModel.type" placeholder="请选择系统类型">
          <a-select-option v-for="item in Object.values(APPLICATION_TYPE_ENUM)" :key="item.value" :value="item.value">
            {{ item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="formModel.status">
          <a-radio v-for="item in Object.values(APPLICATION_STATUS_ENUM)" :key="item.value" :value="item.value">
            {{ item.desc }}
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="所属部门" name="departmentId">
        <a-tree-select
          v-model:value="formModel.departmentId"
          :tree-data="departmentTreeData"
          :field-names="{ children: 'children', label: 'departmentName', value: 'departmentId' }"
          placeholder="请选择所属部门"
          tree-default-expand-all
          @change="onDepartmentChange"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="管理员" name="managerId">
        <a-select v-model:value="formModel.managerId" placeholder="请先选择所属部门" :disabled="!formModel.departmentId">
          <a-select-option v-for="item in managerOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="开发商" name="developerId">
        <a-select v-model:value="formModel.developerId" placeholder="请选择开发商" allow-clear>
          <a-select-option v-for="item in developerOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="开发商联系人" name="developerContactsId">
        <a-select v-model:value="formModel.developerContactsId" placeholder="请选择开发商联系人" allow-clear :disabled="!formModel.developerId">
          <a-select-option v-for="item in developerContactsOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formModel.remark" placeholder="请输入备注" :rows="4" :maxlength="255" showCount />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { applicationApi } from '/@/api/asset/application-api';
  import { developerApi, developerContactApi } from '/@/api/asset/developer-api';
  import { departmentApi } from '/@/api/system/department-api';
  import { employeeApi } from '/@/api/system/employee-api';
  import { APPLICATION_STATUS_ENUM, APPLICATION_TYPE_ENUM, APPLICATION_FORM_FIELDS } from '/@/constants/asset/application-const';
  import { smartSentry } from '/@/lib/smart-sentry';

  // 定义事件
  const emit = defineEmits(['refresh']);

  // ------------------------ 表单相关 ------------------------
  const formRef = ref();
  const visible = ref(false);
  const isEdit = ref(false);
  const currentRecord = ref(null);

  // 表单模型
  const formModel = reactive({
    applicationId: null,
    name: undefined,
    type: undefined,
    status: true,
    managerId: null,
    departmentId: null,
    developerId: null,
    developerContactsId: null,
    remark: undefined,
  });

  // 表单验证规则
  const rules = {
    name: APPLICATION_FORM_FIELDS.name.rules,
    type: APPLICATION_FORM_FIELDS.type.rules,
    status: APPLICATION_FORM_FIELDS.status.rules,
    managerId: APPLICATION_FORM_FIELDS.managerId.rules,
    departmentId: APPLICATION_FORM_FIELDS.departmentId.rules,
    remark: APPLICATION_FORM_FIELDS.remark.rules,
  };

  // 弹窗标题
  const modalTitle = computed(() => {
    return isEdit.value ? '编辑信息系统' : '新增信息系统';
  });

  // ------------------------ 下拉选项 ------------------------
  const managerOptions = ref([]);
  const departmentTreeData = ref([]);
  const developerOptions = ref([]);
  const developerContactsOptions = ref([]);

  // 监听开发商变化，重新加载联系人选项
  watch(
    () => formModel.developerId,
    (newVal) => {
      if (newVal) {
        loadDeveloperContacts(newVal);
      } else {
        formModel.developerContactsId = null;
        developerContactsOptions.value = [];
      }
    }
  );

  // 监听部门变化，重新加载管理员选项
  watch(
    () => formModel.departmentId,
    (newVal) => {
      if (newVal) {
        loadManagersByDepartment(newVal);
      } else {
        formModel.managerId = null;
        managerOptions.value = [];
      }
    }
  );

  // ------------------------ 数据加载 ------------------------
  async function loadManagersByDepartment(departmentId) {
    try {
      const response = await employeeApi.queryEmployeeByDeptId(departmentId);
      managerOptions.value = (response.data || []).map((item) => ({
        value: item.employeeId,
        label: `${item.actualName} (${item.phone})`,
      }));
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  async function loadDepartmentTreeData() {
    try {
      const response = await departmentApi.queryAllDepartment();
      const departmentList = response.data || [];

      // 构建部门树形结构，根据department-list.vue的实现，parentId为0是根节点
      departmentTreeData.value = buildDepartmentTree(departmentList, 0);
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 构建部门树形结构
  function buildDepartmentTree(departmentList, parentId) {
    const children = departmentList.filter((dept) => dept.parentId === parentId) || [];

    if (children.length > 0) {
      return children.map((dept) => ({
        departmentId: dept.departmentId,
        departmentName: dept.departmentName,
        parentId: dept.parentId,
        children: buildDepartmentTree(departmentList, dept.departmentId),
      }));
    }

    return [];
  }

  async function loadDeveloperOptions() {
    try {
      const response = await developerApi.getAllDevelopers();
      developerOptions.value = (response.data || []).map((item) => ({
        value: item.developerId,
        label: item.developerName,
      }));
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  async function loadDeveloperContacts(developerId) {
    try {
      const response = await developerContactApi.getContactsByDeveloperId(developerId);
      developerContactsOptions.value = (response.data || []).map((item) => ({
        value: item.contactId,
        label: `${item.contactName} (${item.contactPhone})`,
      }));
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 部门变化处理函数
  function onDepartmentChange(departmentId) {
    // 清空管理员选择
    formModel.managerId = null;
    if (departmentId) {
      loadManagersByDepartment(departmentId);
    } else {
      managerOptions.value = [];
    }
  }

  // ------------------------ 弹窗操作 ------------------------
  function showModal(record = null) {
    visible.value = true;
    isEdit.value = !!record;
    currentRecord.value = record;

    // 加载下拉选项
    loadDepartmentTreeData();
    loadDeveloperOptions();

    if (record) {
      // 编辑模式，填充表单数据
      Object.assign(formModel, record);
      if (record.developerId) {
        loadDeveloperContacts(record.developerId);
      }
      if (record.departmentId) {
        loadManagersByDepartment(record.departmentId);
      }
    } else {
      // 新增模式，重置表单
      resetForm();
    }
  }

  function resetForm() {
    Object.assign(formModel, {
      applicationId: null,
      name: '',
      type: '',
      status: true,
      managerId: null,
      departmentId: null,
      developerId: null,
      developerContactsId: null,
      remark: '',
    });
    // 清空选项
    managerOptions.value = [];
    developerContactsOptions.value = [];
  }

  function handleCancel() {
    visible.value = false;
    resetForm();
    formRef.value?.resetFields();
  }

  async function handleOk() {
    try {
      await formRef.value?.validateFields();

      if (isEdit.value) {
        await applicationApi.update(formModel);
        message.success('更新成功');
      } else {
        await applicationApi.add(formModel);
        message.success('新增成功');
      }

      visible.value = false;
      resetForm();
      emit('refresh');
    } catch (e) {
      if (e.errorFields) {
        // 表单验证失败
        return;
      }
      smartSentry.captureError(e);
    }
  }

  // 对外暴露方法
  defineExpose({
    showModal,
  });
</script>

<style scoped>
  .ant-form-item {
    margin-bottom: 16px;
  }
</style>
