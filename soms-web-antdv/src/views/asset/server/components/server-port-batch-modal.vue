<template>
  <a-modal v-model:open="visible" title="批量添加端口" width="1000px" :confirm-loading="confirmLoading" @ok="handleSubmit" @cancel="handleCancel">
    <div class="batch-add-container">
      <div class="batch-actions">
        <a-button type="primary" @click="handleAddRow">
          <template #icon><plus-outlined /></template>
          添加行
        </a-button>
        <a-button @click="handleClearAll">
          <template #icon><clear-outlined /></template>
          清空所有
        </a-button>
        <a-divider type="vertical" />
        <span class="tip">提示：可以通过复制粘贴Excel数据快速填充</span>
      </div>

      <a-table :columns="columns" :data-source="dataSource" :pagination="false" size="small" bordered>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'portNumber'">
            <a-input-number v-model:value="record.portNumber" :min="1" :max="65535" placeholder="端口号" size="small" style="width: 100%" />
          </template>

          <template v-if="column.dataIndex === 'portProtocol'">
            <a-select v-model:value="record.portProtocol" size="small" style="width: 100%">
              <a-select-option value="TCP">TCP</a-select-option>
              <a-select-option value="UDP">UDP</a-select-option>
            </a-select>
          </template>

          <template v-if="column.dataIndex === 'portService'">
            <a-input v-model:value="record.portService" placeholder="服务名称" size="small" />
          </template>

          <template v-if="column.dataIndex === 'portPurpose'">
            <a-input v-model:value="record.portPurpose" placeholder="用途描述" size="small" />
          </template>

          <template v-if="column.dataIndex === 'internetAccess'">
            <a-select v-model:value="record.internetAccess" size="small" style="width: 100%">
              <a-select-option v-for="item in internetAccessOptions" :key="item.value" :value="item.value">
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </template>

          <template v-if="column.dataIndex === 'accessControlType'">
            <a-select v-model:value="record.accessControlType" size="small" style="width: 100%" :disabled="record.internetAccess === 0">
              <a-select-option v-for="item in accessControlOptions" :key="item.value" :value="item.value">
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </template>

          <template v-if="column.dataIndex === 'portStatus'">
            <a-select v-model:value="record.portStatus" size="small" style="width: 100%">
              <a-select-option v-for="item in portStatusOptions" :key="item.value" :value="item.value">
                {{ item.desc }}
              </a-select-option>
            </a-select>
          </template>

          <template v-if="column.dataIndex === 'actions'">
            <a-button type="link" size="small" danger @click="handleDeleteRow(index)"> 删除 </a-button>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, ClearOutlined } from '@ant-design/icons-vue';
  import { serverPortApi } from '/@/api/asset/server-port-api';
  import { INTERNET_ACCESS_LEVEL_ENUM, ACCESS_CONTROL_TYPE_ENUM, PORT_STATUS_ENUM } from '/@/constants/asset/server-port-const';

  // Props & Emits
  const props = defineProps({
    serverId: {
      type: Number,
      required: true,
    },
  });

  const emit = defineEmits(['refresh']);

  // 响应式数据
  const visible = ref(false);
  const confirmLoading = ref(false);
  const dataSource = ref([]);

  // 表格列定义
  const columns = [
    {
      title: '端口号',
      dataIndex: 'portNumber',
      width: 100,
    },
    {
      title: '协议',
      dataIndex: 'portProtocol',
      width: 120,
    },
    {
      title: '服务名称',
      dataIndex: 'portService',
      width: 120,
    },
    {
      title: '用途描述',
      dataIndex: 'portPurpose',
    },
    {
      title: '外网开放',
      dataIndex: 'internetAccess',
      width: 120,
    },
    {
      title: '访问控制',
      dataIndex: 'accessControlType',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'portStatus',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 80,
      fixed: 'right',
    },
  ];

  // 选项数据
  const internetAccessOptions = computed(() => Object.values(INTERNET_ACCESS_LEVEL_ENUM));
  const accessControlOptions = computed(() => Object.values(ACCESS_CONTROL_TYPE_ENUM));
  const portStatusOptions = computed(() => Object.values(PORT_STATUS_ENUM));

  // -------------- 暴露的方法 ------------------------
  const show = (existingPorts = []) => {
    visible.value = true;
    initData(existingPorts);
  };

  // -------------- 事件处理 ------------------------
  const handleSubmit = async () => {
    try {
      // 验证数据
      const validData = validateData();
      if (validData.length === 0) {
        message.warning('请至少添加一个有效的端口');
        return;
      }

      confirmLoading.value = true;

      // 转换为API需要的格式
      const submitData = validData.map((item) => ({
        serverId: props.serverId,
        portNumber: item.portNumber,
        portProtocol: item.portProtocol,
        portService: item.portService || '',
        portPurpose: item.portPurpose || '',
        internetAccess: item.internetAccess,
        accessControlType: item.accessControlType,
        allowedSourcesList: [],
        allowedTargetsList: [],
        portStatus: item.portStatus,
      }));

      const res = await serverPortApi.batchAddPorts(submitData);
      if (res.ok) {
        message.success(`成功添加 ${validData.length} 个端口`);
        visible.value = false;
        emit('refresh');
      } else {
        message.error(res.msg || '批量添加失败');
      }
    } catch (error) {
      console.error('批量添加失败:', error);
      message.error('批量添加失败');
    } finally {
      confirmLoading.value = false;
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };

  const handleAddRow = () => {
    dataSource.value.push(createEmptyRow());
  };

  const handleDeleteRow = (index) => {
    dataSource.value.splice(index, 1);
  };

  const handleClearAll = () => {
    initData();
  };

  // -------------- 辅助方法 ------------------------
  const initData = (existingPorts = []) => {
    dataSource.value = [];

    // 先添加现有端口信息
    existingPorts.forEach((port) => {
      dataSource.value.push({
        portNumber: port.portNumber,
        portProtocol: port.portProtocol,
        portService: port.portService || '',
        portPurpose: port.portPurpose || '',
        internetAccess: port.internetAccess || 0,
        accessControlType: port.accessControlType || 0,
        portStatus: port.portStatus || 0,
      });
    });

    // 如果没有现有端口，添加1行空行
    if (existingPorts.length === 0) {
      dataSource.value.push(createEmptyRow());
    }
  };

  const createEmptyRow = () => ({
    portNumber: undefined,
    portProtocol: 'TCP',
    portService: '',
    portPurpose: '',
    internetAccess: 0,
    accessControlType: 0,
    portStatus: 1,
  });

  const validateData = () => {
    return dataSource.value.filter((item) => {
      return item.portNumber && item.portNumber >= 1 && item.portNumber <= 65535 && item.portProtocol;
    });
  };

  // 暴露方法
  defineExpose({
    show,
  });
</script>

<style lang="less" scoped>
  .batch-add-container {
    .batch-actions {
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      .ant-btn {
        margin-right: 8px;
      }

      .tip {
        color: #666;
        font-size: 12px;
      }
    }
  }
</style>
