<!--
  * 服务器详情抽屉 - 现代化设计
  *
  * <AUTHOR>
  * @Date 2025-07-14
-->
<template>
  <a-drawer
    v-model:open="drawerVisible"
    title="服务器详情"
    placement="right"
    width="700"
    :closable="true"
    :mask="true"
    :maskClosable="false"
    class="server-detail-drawer"
  >
    <div class="server-detail-content" v-if="serverDetail">
      <!-- 头部基本信息 -->
      <div class="server-header">
        <div class="server-basic-info">
          <div class="server-icon">
            <OsIcon :operating-system="serverDetail.operatingSystem" />
          </div>
          <div class="server-info">
            <h2 class="server-name">{{ serverDetail.serverName }}</h2>
            <div class="server-tags">
              <a-tag :color="getServerTypeColor(serverDetail.serverType)">
                {{ $smartEnumPlugin.getDescByValue('SERVER_TYPE_ENUM', serverDetail.serverType) }}
              </a-tag>
              <a-tag :color="getPowerStateColor(serverDetail.powerState)">
                {{ $smartEnumPlugin.getDescByValue('POWER_STATE_ENUM', serverDetail.powerState) }}
              </a-tag>
              <a-tag :color="getPurposeSurveyStepColor(serverDetail.purposeSurveyStep)">
                {{ $smartEnumPlugin.getDescByValue('PURPOSE_SURVEY_STEP_ENUM', serverDetail.purposeSurveyStep) }}
              </a-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 硬件配置卡片 -->
      <div class="hardware-cards">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-card class="hardware-card">
              <a-statistic title="CPU" :value="serverDetail.cpuCores || 0" suffix="核" :value-style="{ color: '#1890ff' }">
                <template #prefix>
                  <svg class="icon cpu-icon" aria-hidden="true">
                    <use xlink:href="#icon-cpu" />
                  </svg>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card class="hardware-card">
              <a-statistic title="内存" :value="serverDetail.memorySize || 0" suffix="GB" :value-style="{ color: '#52c41a' }">
                <template #prefix>
                  <svg class="icon memory-icon" aria-hidden="true">
                    <use xlink:href="#icon-memory" />
                  </svg>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :span="8">
            <a-card class="hardware-card">
              <a-statistic title="磁盘" :value="serverDetail.diskSize || 0" suffix="GB" :value-style="{ color: '#fa8c16' }">
                <template #prefix>
                  <svg class="icon disk-icon" aria-hidden="true">
                    <use xlink:href="#icon-disk" />
                  </svg>
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 详细信息标签页 -->
      <div class="detail-info">
        <a-tabs v-model:activeKey="activeTab" type="card">
          <a-tab-pane key="basic" tab="基本信息">
            <!-- 核心信息卡片 -->
            <div class="info-cards">
              <a-row :gutter="[16, 16]">
                <!-- 系统信息卡片 -->
                <a-col :span="12">
                  <a-card size="small" class="info-card">
                    <template #title>
                      <div class="card-title">
                        <desktop-outlined class="title-icon" />
                        系统信息
                      </div>
                    </template>
                    <div class="card-content">
                      <div class="info-item">
                        <span class="label">操作系统:</span>
                        <span class="value"><DictLabel :dict-code="DICT_CODE_ENUM.SERVER_OS" :data-value="serverDetail.operatingSystem" /></span>
                      </div>
                      <div class="info-item">
                        <span class="label">所在集群:</span>
                        <span class="value">{{ serverDetail.vcenterName || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">磁盘数量:</span>
                        <span class="value">{{ serverDetail.diskCount || '-' }} 个</span>
                      </div>
                    </div>
                  </a-card>
                </a-col>

                <!-- 网络信息卡片 -->
                <a-col :span="12">
                  <a-card size="small" class="info-card">
                    <template #title>
                      <div class="card-title">
                        <global-outlined class="title-icon" />
                        网络信息
                        <a-button type="link" size="small" @click="handleConfigManualIp" class="config-btn"> <setting-outlined /> 配置IP </a-button>
                      </div>
                    </template>
                    <div class="card-content">
                      <div class="info-item" v-if="serverDetail.privateIp">
                        <span class="label">主IP:</span>
                        <a-tag color="blue">{{ serverDetail.privateIp || serverDetail.publicIp }}</a-tag>
                      </div>
                      <div class="info-item" v-if="serverDetail.manualIp">
                        <span class="label">手动配置IP:</span>
                        <a-tag color="orange">{{ serverDetail.manualIp }}</a-tag>
                      </div>
                      <div class="info-item" v-if="getIpList(serverDetail.ipList).length > 0">
                        <span class="label">其他IP:</span>
                        <div class="ip-tags">
                          <a-tag v-for="(ip, index) in getIpList(serverDetail.ipList).slice(0, 3)" :key="index" color="cyan" size="small">
                            {{ ip }}
                          </a-tag>
                          <a-tag v-if="getIpList(serverDetail.ipList).length > 3" color="default" size="small">
                            +{{ getIpList(serverDetail.ipList).length - 3 }}
                          </a-tag>
                        </div>
                      </div>
                    </div>
                  </a-card>
                </a-col>

                <!-- 管理信息卡片 -->
                <a-col :span="12">
                  <a-card size="small" class="info-card">
                    <template #title>
                      <div class="card-title">
                        <user-outlined class="title-icon" />
                        管理信息
                      </div>
                    </template>
                    <div class="card-content">
                      <div class="info-item">
                        <span class="label">所属部门:</span>
                        <span class="value">{{ serverDetail.departmentName || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">负责人:</span>
                        <span class="value">{{ serverDetail.responsiblePerson || '-' }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">联系方式:</span>
                        <span class="value">{{ serverDetail.contactInfo || '-' }}</span>
                      </div>
                    </div>
                  </a-card>
                </a-col>

                <!-- 状态信息卡片 -->
                <a-col :span="12">
                  <a-card size="small" class="info-card">
                    <template #title>
                      <div class="card-title">
                        <safety-certificate-outlined class="title-icon" />
                        状态信息
                      </div>
                    </template>
                    <div class="card-content">
                      <div class="info-item">
                        <span class="label">联网状态:</span>
                        <a-tag :color="serverDetail.internetAccessFlag ? 'success' : 'error'" size="small">
                          {{ serverDetail.internetAccessFlag ? '已联网' : '未联网' }}
                        </a-tag>
                      </div>
                      <div class="info-item">
                        <span class="label">关联系统:</span>
                        <a-tag :color="serverDetail.linkedSystemFlag ? 'success' : 'default'" size="small">
                          {{ serverDetail.linkedSystemFlag ? '已关联' : '未关联' }}
                        </a-tag>
                      </div>
                      <div class="info-item">
                        <span class="label">用途:</span>
                        <span class="value">{{ serverDetail.purpose || '-' }}</span>
                      </div>
                    </div>
                  </a-card>
                </a-col>

                <!-- 时间信息卡片 -->
                <a-col :span="24" v-if="serverDetail.createTime || serverDetail.lastSyncTime || serverDetail.description">
                  <a-card size="small" class="info-card">
                    <template #title>
                      <div class="card-title">
                        <clock-circle-outlined class="title-icon" />
                        其他信息
                      </div>
                    </template>
                    <div class="card-content">
                      <div class="info-item" v-if="serverDetail.createTime">
                        <span class="label">创建时间:</span>
                        <span class="value">{{ dayjs(serverDetail.createTime).format('YYYY-MM-DD HH:mm') }}</span>
                      </div>
                      <div class="info-item" v-if="serverDetail.lastSyncTime">
                        <span class="label">最后同步:</span>
                        <span class="value">{{ dayjs(serverDetail.lastSyncTime).format('YYYY-MM-DD HH:mm') }}</span>
                      </div>
                      <div class="info-item" v-if="serverDetail.description">
                        <span class="label">备注:</span>
                        <span class="value description">{{ serverDetail.description }}</span>
                      </div>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </a-tab-pane>

          <a-tab-pane key="ports" tab="端口管理">
            <div class="port-management-simplified">
              <div class="port-actions">
                <a-button type="primary" @click="handleBatchAddPort">
                  <template #icon><plus-square-outlined /></template>
                  批量添加端口
                </a-button>
                <a-button @click="handleQuickAddPort">
                  <template #icon><thunderbolt-outlined /></template>
                  快速添加常用端口
                </a-button>
              </div>
              <server-port-management :server-id="serverDetail.serverId" ref="portManagementRef" />
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 手动配置IP弹窗 -->
      <a-modal
        v-model:open="manualIpModalVisible"
        title="手动配置IP地址"
        @ok="handleSaveManualIp"
        @cancel="manualIpModalVisible = false"
        :confirm-loading="saving"
      >
        <a-form :model="manualIpForm" layout="vertical">
          <a-form-item label="IP地址" name="manualIp">
            <a-input v-model:value="manualIpForm.manualIp" placeholder="请输入IP地址，如：*************" :maxlength="15" />
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="manualIpForm.remark" placeholder="请输入配置备注（可选）" :rows="3" :maxlength="200" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>

    <div class="server-detail-loading" v-else>
      <a-spin size="large" />
    </div>
  </a-drawer>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import {
    DesktopOutlined,
    GlobalOutlined,
    UserOutlined,
    SafetyCertificateOutlined,
    ClockCircleOutlined,
    SettingOutlined,
    PlusSquareOutlined,
    ThunderboltOutlined,
  } from '@ant-design/icons-vue';
  import { serverApi } from '/@/api/asset/server-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { DICT_CODE_ENUM } from '/@/constants/support/dict-const.js';
  import DictLabel from '/@/components/support/dict-label/index.vue';
  import OsIcon from './os-icon.vue';
  import ServerPortManagement from './server-port-management.vue';

  // 抽屉显示控制
  const drawerVisible = ref(false);
  const serverDetail = ref(null);
  const activeTab = ref('basic');

  // 组件引用
  const portManagementRef = ref();

  // 手动配置IP模态框
  const manualIpModalVisible = ref(false);
  const saving = ref(false);
  const manualIpForm = reactive({
    manualIp: '',
    remark: '',
  });

  // 显示抽屉
  async function showDrawer(serverId) {
    drawerVisible.value = true;
    serverDetail.value = null;
    activeTab.value = 'basic';

    try {
      const result = await serverApi.queryById(serverId);
      serverDetail.value = result.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 刷新服务器详情
  async function refreshServerDetail() {
    if (serverDetail.value?.serverId) {
      try {
        const result = await serverApi.queryById(serverDetail.value.serverId);
        serverDetail.value = result.data;
      } catch (e) {
        smartSentry.captureError(e);
      }
    }
  }

  // 状态颜色辅助方法
  function getPowerStateColor(state) {
    const colorMap = {
      POWERED_ON: 'blue',
      POWERED_OFF: 'red',
      SUSPENDED: 'orange',
    };
    return colorMap[state] || 'default';
  }

  function getServerTypeColor(type) {
    const colorMap = {
      0: 'purple', // 物理机
      1: 'cyan', // 虚拟机
      2: 'geekblue', // VMware虚拟机
    };
    return colorMap[type] || 'default';
  }

  function getPurposeSurveyStepColor(status) {
    const colorMap = {
      0: 'default', // 未知
      1: 'orange', // 待确认
      2: 'geekblue', // 已确认
    };
    return colorMap[status] || 'default';
  }

  // IP列表处理
  function getIpList(ipListStr) {
    if (!ipListStr) return [];
    return ipListStr.split(/[,，;；\s]+/).filter((ip) => ip.trim());
  }

  // 手动配置IP相关方法
  function handleConfigManualIp() {
    manualIpForm.manualIp = serverDetail.value?.manualIp || '';
    manualIpForm.remark = '';
    manualIpModalVisible.value = true;
  }

  async function handleSaveManualIp() {
    if (!manualIpForm.manualIp?.trim()) {
      message.warning('请输入IP地址');
      return;
    }

    // 简单的IP格式校验
    const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!ipRegex.test(manualIpForm.manualIp)) {
      message.warning('请输入正确的IP地址格式');
      return;
    }

    try {
      saving.value = true;
      const updateData = {
        serverId: serverDetail.value.serverId,
        manualIp: manualIpForm.manualIp.trim(),
      };

      const result = await serverApi.updateNetworkConfig(updateData);
      if (result.ok) {
        message.success('配置成功');
        manualIpModalVisible.value = false;
        await refreshServerDetail();
      } else {
        message.error(result.msg || '配置失败');
      }
    } catch (e) {
      message.error('配置失败');
      smartSentry.captureError(e);
    } finally {
      saving.value = false;
    }
  }

  // 端口管理相关方法
  function handleBatchAddPort() {
    if (portManagementRef.value?.handleBatchAdd) {
      portManagementRef.value.handleBatchAdd();
    }
  }

  function handleQuickAddPort() {
    if (portManagementRef.value?.handleQuickAdd) {
      portManagementRef.value.handleQuickAdd();
    }
  }

  // 暴露方法
  defineExpose({
    showDrawer,
  });
</script>

<style lang="less" scoped>
  .server-detail-drawer {
    :deep(.ant-drawer-body) {
      padding: 0;
      overflow-x: hidden;
    }
  }

  .server-detail-content {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;

    // 头部基本信息
    .server-header {
      margin-bottom: 24px;

      .server-basic-info {
        display: flex;
        align-items: center;
        gap: 16px;

        .server-icon {
          width: 64px;
          height: 64px;
          display: flex;
          align-items: center;
          justify-content: center;

          :deep(.os-icon-circle) {
            width: 64px;
            height: 64px;
          }

          :deep(.os-icon .icon) {
            width: 48px;
            height: 48px;
          }
        }

        .server-info {
          flex: 1;

          .server-name {
            margin: 0 0 12px 0;
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
          }

          .server-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
          }
        }
      }
    }

    // 硬件配置卡片
    .hardware-cards {
      margin-bottom: 24px;
      width: 100%;
      overflow: hidden;

      .hardware-card {
        text-align: center;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transform: translateY(-2px);
        }

        :deep(.ant-statistic-content) {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .ant-statistic-content-prefix {
            font-size: 18px;

            .icon {
              width: 1em;
              height: 1em;
              fill: currentColor;

              &.cpu-icon {
                color: #1890ff;
              }

              &.memory-icon {
                color: #52c41a;
              }

              &.disk-icon {
                color: #fa8c16;
              }
            }
          }

          .ant-statistic-content-value {
            font-size: 20px;
            font-weight: 600;
          }

          .ant-statistic-content-suffix {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }

    // 详细信息
    .detail-info {
      width: 100%;
      overflow: hidden;
      // 信息卡片区域
      .info-cards {
        width: 100%;

        .info-card {
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .card-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 500;

            .title-icon {
              color: #1890ff;
              margin-right: 8px;
            }

            .config-btn {
              padding: 0;
              height: auto;
              font-size: 12px;
            }
          }

          .card-content {
            .info-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .label {
                color: #666;
                font-size: 13px;
                min-width: 70px;
                margin-right: 8px;
              }

              .value {
                flex: 1;
                font-size: 13px;
                word-break: break-all;
                word-wrap: break-word;

                &.description {
                  color: #666;
                  font-style: italic;
                }
              }

              .ip-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
              }
            }
          }
        }
      }

      // 端口管理简化版
      .port-management-simplified {
        width: 100%;
        overflow-x: hidden;

        .port-actions {
          margin-bottom: 16px;
          text-align: center;

          .ant-btn {
            margin: 0 8px;
            min-width: 140px;
          }
        }

        // 确保端口管理表格不会超出容器
        :deep(.server-port-management) {
          width: 100%;
          overflow-x: hidden;

          .ant-table-wrapper {
            overflow-x: auto;
          }

          .ant-table {
            min-width: 600px;
          }
        }
      }
    }
  }

  .server-detail-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
</style>
