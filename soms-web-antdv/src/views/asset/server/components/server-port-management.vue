<template>
  <div class="server-port-management">
    <a-table :columns="columns" :data-source="portList" :loading="tableLoading" :pagination="false" size="small" bordered>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'portProtocol'">
          <a-tag :color="record.portProtocol === 'TCP' ? 'blue' : 'green'">
            {{ record.portProtocol }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'internetAccess'">
          <a-tag :color="getInternetAccessColor(record.internetAccess)">
            {{ getInternetAccessDesc(record.internetAccess) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'accessControlType'">
          <span v-if="record.internetAccess === 0">-</span>
          <a-tag v-else :color="getAccessControlColor(record.accessControlType)">
            {{ getAccessControlDesc(record.accessControlType) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'portStatus'">
          <a-tag :color="getPortStatusColor(record.portStatus)">
            {{ getPortStatusDesc(record.portStatus) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'actions'">
          <a-space>
            <a-popconfirm title="确定要删除这个端口吗？" @confirm="handleDeletePort(record.portId)">
              <a-button type="link" size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 批量添加弹窗 -->
    <server-port-batch-modal ref="batchModalRef" :server-id="serverId" @refresh="loadPortList" />

    <!-- 快速添加常用端口弹窗 -->
    <server-port-quick-modal ref="quickModalRef" :server-id="serverId" @refresh="loadPortList" />
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { serverPortApi } from '/@/api/asset/server-port-api';
  import { INTERNET_ACCESS_LEVEL_ENUM, ACCESS_CONTROL_TYPE_ENUM, PORT_STATUS_ENUM } from '/@/constants/asset/server-port-const';
  import ServerPortBatchModal from './server-port-batch-modal.vue';
  import ServerPortQuickModal from './server-port-quick-modal.vue';

  // Props
  const props = defineProps({
    serverId: {
      type: Number,
      required: true,
    },
  });

  // 响应式数据
  const tableLoading = ref(false);
  const portList = ref([]);

  // 组件引用
  const batchModalRef = ref();
  const quickModalRef = ref();

  // 表格列定义
  const columns = [
    {
      title: '端口号',
      dataIndex: 'portNumber',
      width: 80,
      sorter: (a, b) => a.portNumber - b.portNumber,
    },
    {
      title: '协议',
      dataIndex: 'portProtocol',
      width: 60,
    },
    {
      title: '服务名称',
      dataIndex: 'portService',
      width: 120,
      ellipsis: true,
    },
    {
      title: '用途描述',
      dataIndex: 'portPurpose',
      ellipsis: true,
    },
    {
      title: '外网开放',
      dataIndex: 'internetAccess',
      width: 100,
    },
    {
      title: '访问控制',
      dataIndex: 'accessControlType',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'portStatus',
      width: 80,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 80,
      fixed: 'right',
    },
  ];

  // -------------- 生命周期 ------------------------
  onMounted(() => {
    loadPortList();
  });

  // -------------- 数据加载 ------------------------
  /**
   * 加载端口列表
   */
  const loadPortList = async () => {
    try {
      tableLoading.value = true;
      const res = await serverPortApi.queryByServerId(props.serverId);
      if (res.data) {
        portList.value = res.data;
      }
    } catch (error) {
      message.error('加载端口列表失败');
    } finally {
      tableLoading.value = false;
    }
  };

  // -------------- 事件处理 ------------------------
  /**
   * 批量添加端口
   */
  const handleBatchAdd = () => {
    batchModalRef.value.show(portList.value);
  };

  /**
   * 快速添加常用端口
   */
  const handleQuickAdd = () => {
    quickModalRef.value.show();
  };

  /**
   * 删除端口
   */
  const handleDeletePort = async (portId) => {
    try {
      const res = await serverPortApi.deletePort(portId);
      if (res.ok) {
        message.success('删除成功');
        loadPortList();
      } else {
        message.error(res.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // -------------- 辅助方法 ------------------------
  const getInternetAccessDesc = (value) => {
    return Object.values(INTERNET_ACCESS_LEVEL_ENUM).find((item) => item.value === value)?.desc || '';
  };

  const getInternetAccessColor = (value) => {
    return Object.values(INTERNET_ACCESS_LEVEL_ENUM).find((item) => item.value === value)?.color || 'default';
  };

  const getAccessControlDesc = (value) => {
    return Object.values(ACCESS_CONTROL_TYPE_ENUM).find((item) => item.value === value)?.desc || '';
  };

  const getAccessControlColor = (value) => {
    return Object.values(ACCESS_CONTROL_TYPE_ENUM).find((item) => item.value === value)?.color || 'default';
  };

  const getPortStatusDesc = (value) => {
    return Object.values(PORT_STATUS_ENUM).find((item) => item.value === value)?.desc || '';
  };

  const getPortStatusColor = (value) => {
    return Object.values(PORT_STATUS_ENUM).find((item) => item.value === value)?.color || 'default';
  };

  // 暴露方法供父组件调用
  defineExpose({
    handleBatchAdd,
    handleQuickAdd,
    loadPortList,
  });
</script>

<style lang="less" scoped>
  .server-port-management {
    .port-actions {
      margin-bottom: 16px;

      .ant-btn {
        margin-right: 8px;
      }
    }
  }
</style>
