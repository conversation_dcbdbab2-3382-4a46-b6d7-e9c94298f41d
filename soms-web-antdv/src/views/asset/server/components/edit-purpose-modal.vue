<!--
  * 编辑服务器用途弹窗
  *
  * <AUTHOR>
  * @Date 2025-07-15
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="编辑服务器用途"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    width="500px"
  >
    <a-form :model="formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }" ref="formRef">
      <a-form-item label="服务器名称">
        <a-input v-model:value="formData.serverName" disabled />
      </a-form-item>

      <a-form-item label="调研情况" name="purposeSurveyStatus">
        <SmartEnumSelect width="100%" v-model:value="formData.purposeSurveyStatus" enumName="PURPOSE_SURVEY_STEP_ENUM" placeholder="请选择调研情况" />
      </a-form-item>

      <a-form-item label="服务器用途" name="purpose">
        <a-textarea v-model:value="formData.purpose" :rows="4" placeholder="请输入服务器用途描述" :maxlength="500" show-count />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { serverApi } from '/@/api/asset/server-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';

  // 弹窗显示状态
  const modalVisible = ref(false);
  const confirmLoading = ref(false);

  // 表单引用
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    serverId: undefined,
    serverName: '',
    purpose: '',
    purposeSurveyStatus: 2, // 默认为"已确认"
  });

  // 表单验证规则
  const rules = {
    purpose: [
      { required: true, message: '请输入服务器用途', trigger: 'blur' },
      { max: 500, message: '用途描述不能超过500个字符', trigger: 'blur' },
    ],
    purposeSurveyStatus: [{ required: true, message: '请选择调研情况', trigger: 'change' }],
  };

  // 定义 emit 事件
  const emit = defineEmits(['refresh']);

  /**
   * 显示弹窗
   */
  function showModal(server) {
    formData.serverId = server.serverId;
    formData.serverName = server.serverName;
    formData.purpose = server.purpose || '';
    formData.purposeSurveyStatus = server.purposeSurveyStatus ?? 2; // 默认为"已确认"
    modalVisible.value = true;
  }

  /**
   * 确认提交
   */
  async function handleOk() {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      const updateData = {
        serverId: formData.serverId,
        purpose: formData.purpose,
        purposeSurveyStatus: formData.purposeSurveyStatus,
      };

      await serverApi.updatePurpose(updateData);
      message.success('服务器用途更新成功！');

      modalVisible.value = false;
      emit('refresh');
    } catch (error) {
      if (error.errorFields) {
        // 表单验证失败
        return;
      }
      smartSentry.captureError(error);
    } finally {
      confirmLoading.value = false;
    }
  }

  /**
   * 取消操作
   */
  function handleCancel() {
    modalVisible.value = false;
    formRef.value?.resetFields();
  }

  // 暴露方法供父组件调用
  defineExpose({
    showModal,
  });
</script>

<style lang="less" scoped>
  .ant-form-item {
    margin-bottom: 16px;
  }
</style>
