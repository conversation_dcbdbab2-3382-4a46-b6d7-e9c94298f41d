<!--
  * 服务器列表
  *
  * <AUTHOR>
  * @Date 2025-07-14
-->
<template>
  <div>
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 300px" v-model:value="queryForm.keyword" placeholder="服务器名称/主机名/IP地址" />
        </a-form-item>

        <a-form-item label="服务器类型" class="smart-query-form-item">
          <SmartEnumSelect width="120px" v-model:value="queryForm.serverType" enumName="SERVER_TYPE_ENUM" placeholder="请选择类型" />
        </a-form-item>

        <a-form-item label="运行状态" class="smart-query-form-item">
          <SmartEnumSelect width="120px" v-model:value="queryForm.powerState" enumName="POWER_STATE_ENUM" placeholder="请选择状态" />
        </a-form-item>

        <a-form-item label="集群" class="smart-query-form-item">
          <a-select style="width: 180px" v-model:value="queryForm.clusterName" placeholder="请选择集群" allowClear>
            <a-select-option v-for="cluster in clusterList" :key="cluster" :value="cluster">
              {{ cluster }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button-group>
            <a-button type="primary" @click="queryPage">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>

            <a-button @click="resetQuery">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-button-group>
          <a-button class="smart-margin-left20" @click="moreQueryConditionFlag = !moreQueryConditionFlag">
            <template #icon>
              <MoreOutlined />
            </template>
            {{ moreQueryConditionFlag ? '收起' : '展开' }}
          </a-button>
        </a-form-item>
      </a-row>

      <a-row class="smart-query-form-row" v-show="moreQueryConditionFlag">
        <a-form-item label="调研情况" class="smart-query-form-item">
          <SmartEnumSelect width="120px" v-model:value="queryForm.purposeSurveyStep" enumName="PURPOSE_SURVEY_STEP_ENUM" placeholder="请选择状态" />
        </a-form-item>

        <a-form-item label="所属部门" class="smart-query-form-item">
          <a-tree-select
            style="width: 200px"
            v-model:value="queryForm.departmentId"
            placeholder="请选择部门"
            :field-names="{ children: 'children', label: 'departmentName', value: 'departmentId' }"
            :tree-data="departmentTreeData"
            tree-default-expand-all
            allowClear
          />
        </a-form-item>

        <a-form-item label="负责人" class="smart-query-form-item">
          <a-select
            style="width: 150px"
            v-model:value="queryForm.responsiblePerson"
            placeholder="请选择负责人"
            allowClear
            show-search
            :filter-option="filterOption"
          >
            <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.actualName">
              {{ employee.actualName }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-row>
    </a-form>

    <a-card size="small" :bordered="false" :hoverable="true">
      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button v-privilege="'server:sync'" type="primary" @click="manualSync" :loading="syncLoading">
            <template #icon>
              <SyncOutlined />
            </template>
            手动同步
          </a-button>

          <a-button v-privilege="'server:export'" @click="exportList" :loading="exportLoading">
            <template #icon>
              <DownloadOutlined />
            </template>
            导出
          </a-button>

          <a-button @click="showSyncHistory">
            <template #icon>
              <HistoryOutlined />
            </template>
            同步历史
          </a-button>
        </div>
      </a-row>

      <a-row :gutter="[16, 16]" v-if="!tableLoading && tableData.length > 0">
        <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" :xxl="4" v-for="server in tableData" :key="server.serverId">
          <a-card class="server-card" hoverable @click="showServerDetail(server)">
            <template #title>
              <div class="server-card-title">
                <div class="server-name-with-icon">
                  <OsIcon :operating-system="server.operatingSystem" />
                  <span class="server-name">{{ server.serverName }}</span>
                </div>
                <div class="server-tags">
                  <a-tag :color="getServerTypeColor(server.serverType)" size="small">
                    {{ $smartEnumPlugin.getDescByValue('SERVER_TYPE_ENUM', server.serverType) }}
                  </a-tag>
                  <a-tag :color="getPowerStateColor(server.powerState)" size="small">
                    {{ $smartEnumPlugin.getDescByValue('POWER_STATE_ENUM', server.powerState) }}
                  </a-tag>
                </div>
              </div>
            </template>

            <div class="server-card-content">
              <div class="server-info-list">
                <div class="info-item">
                  <span class="info-label">集群:</span>
                  <span class="info-value">{{ server.vcenterName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">主IP:</span>
                  <span class="info-value">{{ server.privateIp || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">用途:</span>
                  <span class="info-value">
                    <a-tag :color="getPurposeSurveyStepColor(server.purposeSurveyStep)" size="small">
                      {{ $smartEnumPlugin.getDescByValue('PURPOSE_SURVEY_STEP_ENUM', server.purposeSurveyStep) }}
                    </a-tag>
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">负责人:</span>
                  <span class="info-value">{{ server.responsiblePerson || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">最后更新:</span>
                  <span class="info-value">{{ server.lastSyncTime ? dayjs(server.lastSyncTime).format('MM-DD HH:mm') : '-' }}</span>
                </div>
              </div>

              <div class="server-specs">
                <div class="spec-item">
                  <span class="spec-label">CPU</span>
                  <span class="spec-value">{{ server.cpuCores || '-' }} 核</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">内存</span>
                  <span class="spec-value">{{ server.memorySize || '-' }} GB</span>
                </div>
                <div class="spec-item">
                  <span class="spec-label">磁盘</span>
                  <span class="spec-value">{{ server.diskSize || '-' }} GB</span>
                </div>
              </div>
            </div>

            <template #actions>
              <a-button v-if="server.serverType === 2" v-privilege="'server:operate'" type="link" size="small" @click.stop="showVmOperations(server)">
                虚拟机操作
              </a-button>
              <a-button v-privilege="'server:edit'" type="link" size="small" @click.stop="showEditPurpose(server)"> 编辑用途 </a-button>
            </template>
          </a-card>
        </a-col>
      </a-row>

      <div v-if="tableLoading" class="server-cards-loading">
        <a-spin size="large" />
      </div>

      <div v-if="!tableLoading && tableData.length === 0" class="server-cards-empty">
        <a-empty description="暂无数据" />
      </div>

      <div class="smart-query-table-page">
        <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryPage"
          :show-total="(total) => `共${total}条`"
        />
      </div>
    </a-card>

    <!-- 虚拟机操作模态框 -->
    <VmOperationsModal ref="vmOperationsModalRef" @refresh="queryPage" />

    <!-- 服务器详情抽屉 -->
    <ServerDetailDrawer ref="serverDetailDrawerRef" />

    <!-- 同步历史记录抽屉 -->
    <SyncHistoryDrawer ref="syncHistoryDrawerRef" />

    <!-- 编辑用途弹窗 -->
    <EditPurposeModal ref="editPurposeModalRef" @refresh="queryPage" />
  </div>
</template>

<script setup>
  import { message } from 'ant-design-vue';
  import { onMounted, reactive, ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { SearchOutlined, ReloadOutlined, MoreOutlined, SyncOutlined, DownloadOutlined, HistoryOutlined } from '@ant-design/icons-vue';
  import { serverApi } from '/@/api/asset/server-api';
  import { departmentApi } from '/@/api/system/department-api';
  import { employeeApi } from '/@/api/system/employee-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import VmOperationsModal from './components/vm-operations-modal.vue';
  import ServerDetailDrawer from './components/server-detail-drawer.vue';
  import SyncHistoryDrawer from './components/sync-history-drawer.vue';
  import EditPurposeModal from './components/edit-purpose-modal.vue';
  import OsIcon from './components/os-icon.vue';

  // ------------------------ 查询表单 ------------------------
  const queryFormState = {
    keyword: undefined,
    serverType: undefined,
    powerState: 'POWERED_ON', // 默认显示开机状态的服务器
    clusterName: undefined,
    purposeSurveyStep: undefined,
    departmentId: undefined,
    responsiblePerson: undefined,
    pageNum: 1,
    pageSize: 12,
  };
  const queryForm = reactive({ ...queryFormState });
  // 展开更多查询参数
  const moreQueryConditionFlag = ref(false);

  // ------------------------ table表格数据和查询方法 ------------------------
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // ------------------------ 集群列表数据 ------------------------
  const clusterList = ref([]);

  // ------------------------ 部门和员工数据 ------------------------
  const departmentTreeData = ref([]);
  const employeeList = ref([]);
  const allEmployeeList = ref([]);

  // 重置查询
  function resetQuery() {
    const pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryPage();
  }

  // 分页查询
  async function queryPage() {
    try {
      tableLoading.value = true;
      const responseModel = await serverApi.queryPage(queryForm);
      tableData.value = responseModel.data.list || [];
      total.value = responseModel.data.total || 0;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // 加载集群列表
  async function loadClusterList() {
    try {
      const response = await serverApi.getAllClusters();
      clusterList.value = response.data || [];
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 加载部门树形数据
  async function loadDepartmentTree() {
    try {
      const response = await departmentApi.queryAllDepartment();
      const departmentList = response.data || [];

      departmentTreeData.value = buildDepartmentTree(departmentList, 0);
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 构建部门树形结构
  function buildDepartmentTree(departmentList, parentId) {
    const children = departmentList.filter((dept) => dept.parentId === parentId) || [];

    if (children.length > 0) {
      return children.map((dept) => ({
        departmentId: dept.departmentId,
        departmentName: dept.departmentName,
        parentId: dept.parentId,
        children: buildDepartmentTree(departmentList, dept.departmentId),
      }));
    }

    return [];
  }

  // 加载所有员工数据
  async function loadAllEmployees() {
    try {
      const response = await employeeApi.queryAll();
      allEmployeeList.value = response.data || [];
      employeeList.value = allEmployeeList.value;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 根据部门筛选员工
  function filterEmployeesByDepartment() {
    if (queryForm.departmentId) {
      employeeList.value = allEmployeeList.value.filter((emp) => emp.departmentId === queryForm.departmentId);
    } else {
      employeeList.value = allEmployeeList.value;
    }
    // 如果当前选中的负责人不在新的员工列表中，清空选择
    if (queryForm.responsiblePerson && !employeeList.value.some((emp) => emp.actualName === queryForm.responsiblePerson)) {
      queryForm.responsiblePerson = undefined;
    }
  }

  // 员工下拉搜索过滤
  function filterOption(input, option) {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  }

  // 监听部门变化，筛选员工
  watch(
    () => queryForm.departmentId,
    () => {
      filterEmployeesByDepartment();
    }
  );

  onMounted(() => {
    loadClusterList();
    loadDepartmentTree();
    loadAllEmployees();
    queryPage();
  });

  // -------------- 同步操作 --------------
  const syncLoading = ref(false);

  async function manualSync() {
    try {
      syncLoading.value = true;
      const result = await serverApi.manualSync();
      message.success(`同步完成！新增: ${result.data.added || 0}, 更新: ${result.data.updated || 0}`);
      queryPage();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      syncLoading.value = false;
    }
  }

  // -------------- 导出操作 --------------
  const exportLoading = ref(false);

  async function exportList() {
    try {
      exportLoading.value = true;
      const response = await serverApi.exportList(queryForm);

      // 创建下载链接
      const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `服务器列表_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('导出成功!');
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      exportLoading.value = false;
    }
  }

  // -------------- 模态框操作 --------------
  const vmOperationsModalRef = ref();
  const serverDetailDrawerRef = ref();
  const syncHistoryDrawerRef = ref();
  const editPurposeModalRef = ref();

  // 显示虚拟机操作
  function showVmOperations(record) {
    vmOperationsModalRef.value.showModal(record);
  }

  // 显示同步历史
  function showSyncHistory() {
    syncHistoryDrawerRef.value.showDrawer();
  }

  // 显示服务器详情抽屉
  function showServerDetail(server) {
    serverDetailDrawerRef.value.showDrawer(server.serverId);
  }

  // 显示编辑用途弹窗
  function showEditPurpose(server) {
    editPurposeModalRef.value.showModal(server);
  }

  // -------------- 状态显示辅助方法 --------------
  function getPowerStateColor(state) {
    const colorMap = {
      POWERED_ON: 'blue',
      POWERED_OFF: 'red',
      SUSPENDED: 'orange',
    };
    return colorMap[state] || 'default';
  }

  function getServerTypeColor(type) {
    const colorMap = {
      0: 'purple', // 物理机
      1: 'cyan', // 虚拟机
      2: 'geekblue', // VMware虚拟机
    };
    return colorMap[type] || 'default';
  }

  // 获取用途调研状态颜色
  function getPurposeSurveyStepColor(status) {
    const colorMap = {
      0: 'default', // 未知
      1: 'orange', // 待确认
      2: 'geekblue', // 已确认
    };
    return colorMap[status] || 'gray';
  }
</script>

<style lang="less" scoped>
  // 通用样式 mixins
  .text-ellipsis() {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .center-layout() {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
  }

  .remove-last-margin() {
    &:last-child {
      margin-bottom: 0;
    }
  }

  .server-card {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .server-card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 8px;

      .server-name-with-icon {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .server-name {
          font-weight: 600;
          color: #1890ff;
          font-size: 16px;
          margin-left: 8px;
          min-width: 0;
          .text-ellipsis();
        }
      }

      .server-tags {
        display: flex;
        gap: 4px;
        flex-shrink: 0;
        justify-content: flex-end;
      }
    }

    .server-card-content {
      .server-info-list {
        margin-bottom: 12px;

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          min-height: 20px;
          margin-bottom: 6px;
          .remove-last-margin();

          .info-label {
            font-size: 12px;
            min-width: 50px;
            margin-right: 6px;
          }

          .info-value {
            font-size: 12px;
            flex: 1;
            text-align: right;
            .text-ellipsis();
          }
        }
      }

      .server-specs {
        display: flex;
        justify-content: space-between;
        padding: 8px;
        background: #f5f5f5;
        border-radius: 4px;

        .spec-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;

          .spec-label {
            font-size: 11px;
            margin-bottom: 2px;
          }

          .spec-value {
            font-size: 13px;
            font-weight: 600;
          }
        }
      }
    }
  }

  .server-cards-loading,
  .server-cards-empty {
    .center-layout();
  }

  :deep(.ant-tag) {
    margin-inline-end: 0;
  }

  @media (max-width: 1200px) {
    .server-card .server-info-list .info-item .info-value {
      text-align: left;
    }
  }

  @media (max-width: 768px) {
    .server-card .server-specs {
      flex-direction: column;

      .spec-item {
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 8px;
        .remove-last-margin();
      }
    }
  }
</style>
