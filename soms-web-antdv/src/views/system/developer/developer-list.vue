<template>
  <div>
    <!-- 查询表单 -->
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="开发商名称">
          <a-input v-model:value="queryForm.developerName" placeholder="请输入开发商名称" />
        </a-form-item>
        <a-form-item label="地点">
          <a-input v-model:value="queryForm.location" placeholder="请输入地点" />
        </a-form-item>
        <a-form-item label="关键字">
          <a-input v-model:value="queryForm.keyword" placeholder="请输入关键字（开发商名称、地点）" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="queryPage">查询</a-button>
          <a-button @click="resetQuery">重置</a-button>
        </a-form-item>
      </a-row>
    </a-form>

    <!-- 主要内容区域 -->
    <a-card size="small" :bordered="false" :hoverable="true">
      <!-- 操作按钮 -->
      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button type="primary" @click="showAddModal" v-privilege="'developer:add'">
            <template #icon>
              <PlusOutlined />
            </template>
            新增
          </a-button>
          <a-button @click="showImportModal" v-privilege="'developer:import'">
            <template #icon>
              <ImportOutlined />
            </template>
            导入
          </a-button>
          <a-button @click="exportData" v-privilege="'developer:export'">
            <template #icon>
              <ExportOutlined />
            </template>
            导出
          </a-button>
          <a-button :disabled="!hasSelectedItems" @click="batchDelete" v-privilege="'developer:delete'">
            <template #icon>
              <DeleteOutlined />
            </template>
            批量删除
          </a-button>
        </div>
      </a-row>

      <!-- 表格 -->
      <a-table
        :columns="columns"
        :dataSource="tableData"
        :loading="tableLoading"
        :pagination="false"
        rowKey="developerId"
        :rowSelection="rowSelection"
        :expandedRowRender="expandedRowRender"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'contactCount'">
            <a-tag color="blue">{{ record.contactCount || 0 }}</a-tag>
          </template>

          <template v-else-if="column.key === 'createTime'">
            {{ record.createTime }}
          </template>

          <template v-else-if="column.key === 'updateTime'">
            {{ record.updateTime }}
          </template>

          <template v-else-if="column.key === 'action'">
            <div class="smart-table-operate">
              <a-button type="link" @click="showEditModal(record)" v-privilege="'developer:edit'"> 编辑 </a-button>
              <a-button type="link" @click="showAddContactModal(record)" v-privilege="'developer:contact:add'"> 添加联系人 </a-button>
              <a-button type="link" @click="deleteItem(record)" v-privilege="'developer:delete'"> 删除 </a-button>
            </div>
          </template>
        </template>
      </a-table>

      <!-- 分页 -->
      <div class="smart-query-table-page">
        <a-pagination
          showSizeChanger
          showQuickJumper
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryPage"
          @showSizeChange="queryPage"
        />
      </div>
    </a-card>

    <!-- 弹窗组件 -->
    <DeveloperFormModal ref="formModalRef" @refresh="queryPage" />
    <DeveloperImportModal ref="importModalRef" @refresh="queryPage" />
    <DeveloperContactFormModal ref="contactFormModalRef" @refresh="queryPage" />
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, computed, h } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { PlusOutlined, ImportOutlined, ExportOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { developerApi } from '/@/api/asset/developer-api';
  import { DEVELOPER_TABLE_COLUMNS } from '/@/constants/asset/developer-const';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import DeveloperFormModal from './components/developer-form-modal.vue';
  import DeveloperImportModal from './components/developer-import-modal.vue';
  import DeveloperContactFormModal from './components/developer-contact-form-modal.vue';
  import ContactNestedTable from './components/contact-nested-table.vue';

  // ------------------------ 查询表单 ------------------------
  const queryFormState = {
    developerName: '',
    location: '',
    keyword: '',
    pageNum: 1,
    pageSize: 10,
  };
  const queryForm = reactive({ ...queryFormState });

  // ------------------------ 表格数据 ------------------------
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 表格列配置
  const columns = DEVELOPER_TABLE_COLUMNS;

  // 查询分页
  async function queryPage() {
    try {
      tableLoading.value = true;
      const responseModel = await developerApi.queryPage(queryForm);
      tableData.value = responseModel.data.list || [];
      total.value = responseModel.data.total || 0;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // 重置查询
  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    queryPage();
  }

  // 页面加载
  onMounted(() => {
    queryPage();
  });

  // ------------------------ 表格选择 ------------------------
  const selectedRowKeys = ref([]);
  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (keys) => {
      selectedRowKeys.value = keys;
    },
  };

  const hasSelectedItems = computed(() => selectedRowKeys.value.length > 0);

  // ------------------------ 嵌套表格配置 ------------------------
  /**
   * 展开行渲染函数
   */
  const expandedRowRender = (record) => {
    return h(ContactNestedTable, {
      developerId: record.developerId,
      contacts: record.contacts || [],
    });
  };


  // ------------------------ 模态框操作 ------------------------
  const formModalRef = ref();
  const importModalRef = ref();
  const contactFormModalRef = ref();

  function showAddModal() {
    formModalRef.value.showModal();
  }

  function showEditModal(record) {
    formModalRef.value.showModal(record);
  }

  function showImportModal() {
    importModalRef.value.showModal();
  }

  function showAddContactModal(record) {
    contactFormModalRef.value.showModal(null, record.developerId);
  }

  // ------------------------ 操作方法 ------------------------
  // 删除单个项目
  function deleteItem(record) {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除开发商"${record.developerName}"吗？删除后该开发商的所有联系人也将被删除。`,
      onOk: async () => {
        try {
          await developerApi.delete(record.developerId);
          message.success('删除成功');
          queryPage();
        } catch (e) {
          smartSentry.captureError(e);
        }
      },
    });
  }

  // 批量删除
  function batchDelete() {
    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.value.length} 个开发商吗？删除后这些开发商的所有联系人也将被删除。`,
      onOk: async () => {
        try {
          await developerApi.batchDelete(selectedRowKeys.value);
          message.success('删除成功');
          selectedRowKeys.value = [];
          queryPage();
        } catch (e) {
          smartSentry.captureError(e);
        }
      },
    });
  }

  // 导出数据
  async function exportData() {
    try {
      const responseModel = await developerApi.getExportData(queryForm);
      const exportData = responseModel.data;

      // 构建导出文件内容
      const headers = ['开发商名称', '地点', '联系人数量', '创建时间', '更新时间'];
      const csvContent = [
        headers.join(','),
        ...exportData.map((item) =>
          [
            item.developerName || '',
            item.location || '',
            item.contactCount || 0,
            item.createTime || '',
            item.updateTime || '',
          ].join(',')
        ),
      ].join('\n');

      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = `开发商列表_${new Date().toISOString().slice(0, 10)}.csv`;
      link.click();

      message.success('导出成功');
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
</script>

<style scoped>
  .smart-query-form {
    background: #fff;
    border-radius: 6px;
    padding: 24px;
    margin-bottom: 16px;
  }

  .smart-query-form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .smart-query-form-row .ant-form-item {
    margin-bottom: 0;
  }

  .smart-table-btn-block {
    margin-bottom: 16px;
  }

  .smart-table-operate-block {
    display: flex;
    gap: 8px;
  }

  .smart-table-operate {
    display: flex;
    gap: 8px;
  }

  .smart-query-table-page {
    margin-top: 16px;
    text-align: right;
  }
</style>