<template>
  <div class="contact-nested-table">

    <!-- 联系人表格 -->
    <a-table
      :columns="contactColumns"
      :dataSource="contactData"
      :loading="tableLoading"
      :pagination="false"
      rowKey="contactId"
      size="small"
      :scroll="{ x: 500 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'createTime'">
          {{ record.createTime }}
        </template>

        <template v-else-if="column.key === 'action'">
          <div class="nested-table-operate">
            <a-button type="link" size="small" @click="showEditContactModal(record)" v-privilege="'developer:contact:edit'">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="deleteContact(record)" v-privilege="'developer:contact:delete'">
              删除
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { developerContactApi } from '/@/api/asset/developer-api';
  import { DEVELOPER_CONTACT_TABLE_COLUMNS } from '/@/constants/asset/developer-const';
  import { smartSentry } from '/@/lib/smart-sentry';

  const props = defineProps({
    developerId: {
      type: Number,
      required: true,
    },
    contacts: {
      type: Array,
      default: () => [],
    },
  });

  // 表格数据
  const tableLoading = ref(false);
  const contactData = ref([]);

  // 表格列配置
  const contactColumns = DEVELOPER_CONTACT_TABLE_COLUMNS;

  /**
   * 加载联系人数据
   */
  async function loadContacts() {
    if (!props.developerId) return;

    try {
      tableLoading.value = true;
      const response = await developerContactApi.getContactsByDeveloperId(props.developerId);
      contactData.value = response.data || [];
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      tableLoading.value = false;
    }
  }

  /**
   * 显示编辑联系人弹窗
   */
  function showEditContactModal(record) {
    // 这个功能现在由主表格中的联系人弹窗处理
    // 由于嵌套表格已简化，这里暂时保留以防需要
  }

  /**
   * 删除联系人
   */
  function deleteContact(record) {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除联系人"${record.contactName}"吗？`,
      onOk: async () => {
        try {
          await developerContactApi.delete(record.contactId);
          message.success('删除成功');
          loadContacts();
        } catch (error) {
          smartSentry.captureError(error);
        }
      },
    });
  }

  // 页面加载
  onMounted(() => {
    // 优先使用传入的联系人数据，如果没有则重新加载
    if (props.contacts && props.contacts.length > 0) {
      contactData.value = props.contacts;
    } else {
      loadContacts();
    }
  });
</script>

<style scoped>
  .contact-nested-table {
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
  }

  .nested-table-header {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
  }

  .nested-table-operate {
    display: flex;
    gap: 4px;
  }

  .nested-table-operate .ant-btn-link {
    padding: 0 4px;
    height: auto;
  }
</style>