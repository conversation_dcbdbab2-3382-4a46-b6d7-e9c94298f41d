<template>
  <a-modal
    :title="isAdd ? '新增联系人' : '编辑联系人'"
    v-model:open="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="600"
  >
    <a-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="所属开发商" name="developerId">
        <a-select 
          v-model:value="formData.developerId" 
          placeholder="请选择开发商"
          :disabled="!isAdd"
        >
          <a-select-option v-for="developer in developerList" :key="developer.developerId" :value="developer.developerId">
            {{ developer.developerName }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="联系人姓名" name="contactName">
        <a-input v-model:value="formData.contactName" placeholder="请输入联系人姓名" />
      </a-form-item>

      <a-form-item label="联系电话" name="contactPhone">
        <a-input v-model:value="formData.contactPhone" placeholder="请输入联系电话" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { developerApi, developerContactApi } from '/@/api/asset/developer-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emit = defineEmits(['refresh']);

  // 模态框状态
  const visible = ref(false);
  const confirmLoading = ref(false);
  const isAdd = ref(true);

  // 表单引用
  const formRef = ref();

  // 开发商列表
  const developerList = ref([]);

  // 表单数据
  const formData = reactive({
    contactId: undefined,
    developerId: undefined,
    contactName: '',
    contactPhone: '',
  });

  // 表单验证规则
  const rules = {
    developerId: [
      { required: true, message: '请选择开发商', trigger: 'change' },
    ],
    contactName: [
      { required: true, message: '请输入联系人姓名', trigger: 'blur' },
      { max: 50, message: '联系人姓名长度不能超过50个字符', trigger: 'blur' },
    ],
    contactPhone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { max: 20, message: '联系电话长度不能超过20个字符', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '联系电话格式不正确', trigger: 'blur' },
    ],
  };

  /**
   * 显示模态框
   */
  function showModal(record, developerId) {
    visible.value = true;
    isAdd.value = !record;

    if (record) {
      // 编辑模式
      formData.contactId = record.contactId;
      formData.developerId = record.developerId;
      formData.contactName = record.contactName;
      formData.contactPhone = record.contactPhone;
    } else {
      // 新增模式
      resetForm();
      if (developerId) {
        formData.developerId = developerId;
      }
    }
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formData.contactId = undefined;
    formData.developerId = undefined;
    formData.contactName = '';
    formData.contactPhone = '';
    
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }

  /**
   * 确认提交
   */
  async function handleOk() {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      if (isAdd.value) {
        await developerContactApi.add(formData);
        message.success('新增成功');
      } else {
        await developerContactApi.update(formData);
        message.success('更新成功');
      }

      visible.value = false;
      emit('refresh');
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      smartSentry.captureError(error);
    } finally {
      confirmLoading.value = false;
    }
  }

  /**
   * 取消操作
   */
  function handleCancel() {
    visible.value = false;
    resetForm();
  }

  /**
   * 加载开发商列表
   */
  async function loadDeveloperList() {
    try {
      const response = await developerApi.getAllDevelopers();
      developerList.value = response.data || [];
    } catch (error) {
      smartSentry.captureError(error);
    }
  }

  // 页面加载
  onMounted(() => {
    loadDeveloperList();
  });

  // 暴露方法
  defineExpose({
    showModal,
  });
</script>