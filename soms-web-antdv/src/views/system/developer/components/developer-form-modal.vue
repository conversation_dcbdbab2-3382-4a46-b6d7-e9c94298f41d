<template>
  <a-modal
    :title="isAdd ? '新增开发商' : '编辑开发商'"
    v-model:open="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="600"
  >
    <a-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="开发商名称" name="developerName">
        <a-input v-model:value="formData.developerName" placeholder="请输入开发商名称" />
      </a-form-item>

      <a-form-item label="省市" name="provinceCity">
        <a-cascader
          v-model:value="formData.provinceCity"
          :options="PROVINCE_CITY_DATA"
          placeholder="请选择省市"
          :show-search="true"
          @change="handleProvinceCityChange"
        />
      </a-form-item>

      <a-form-item label="详细地址" name="location">
        <a-input v-model:value="formData.location" placeholder="请输入详细地址" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { developerApi } from '/@/api/asset/developer-api';
  import { PROVINCE_CITY_DATA } from '/@/constants/asset/developer-const';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emit = defineEmits(['refresh']);

  // 模态框状态
  const visible = ref(false);
  const confirmLoading = ref(false);
  const isAdd = ref(true);

  // 表单引用
  const formRef = ref();

  // 表单数据
  const formData = reactive({
    developerId: undefined,
    developerName: '',
    province: '',
    city: '',
    provinceCity: [], // 级联选择器绑定值
    location: '',
  });

  // 表单验证规则
  const rules = {
    developerName: [
      { required: true, message: '请输入开发商名称', trigger: 'blur' },
      { max: 100, message: '开发商名称长度不能超过100个字符', trigger: 'blur' },
    ],
    provinceCity: [
      { required: true, message: '请选择省市', trigger: 'change' },
    ],
    location: [
      { max: 200, message: '详细地址长度不能超过200个字符', trigger: 'blur' },
    ],
  };

  /**
   * 省市级联选择变化处理
   */
  function handleProvinceCityChange(value) {
    if (value && value.length === 2) {
      formData.province = value[0];
      formData.city = value[1];
    } else {
      formData.province = '';
      formData.city = '';
    }
  }

  /**
   * 显示模态框
   */
  function showModal(record) {
    visible.value = true;
    isAdd.value = !record;

    if (record) {
      // 编辑模式
      formData.developerId = record.developerId;
      formData.developerName = record.developerName;
      formData.province = record.province || '';
      formData.city = record.city || '';
      formData.provinceCity = record.province && record.city ? [record.province, record.city] : [];
      formData.location = record.location || '';
    } else {
      // 新增模式
      resetForm();
    }
  }

  /**
   * 重置表单
   */
  function resetForm() {
    formData.developerId = undefined;
    formData.developerName = '';
    formData.province = '';
    formData.city = '';
    formData.provinceCity = [];
    formData.location = '';
    
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }

  /**
   * 确认提交
   */
  async function handleOk() {
    try {
      await formRef.value.validate();
      confirmLoading.value = true;

      if (isAdd.value) {
        await developerApi.add(formData);
        message.success('新增成功');
      } else {
        await developerApi.update(formData);
        message.success('更新成功');
      }

      visible.value = false;
      emit('refresh');
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      smartSentry.captureError(error);
    } finally {
      confirmLoading.value = false;
    }
  }

  /**
   * 取消操作
   */
  function handleCancel() {
    visible.value = false;
    resetForm();
  }

  // 暴露方法
  defineExpose({
    showModal,
  });
</script>