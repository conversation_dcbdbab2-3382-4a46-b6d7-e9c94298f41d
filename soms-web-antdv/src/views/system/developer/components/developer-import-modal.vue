<template>
  <a-modal
    title="导入开发商"
    v-model:open="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="600"
  >
    <div class="import-modal-content">
      <a-alert
        message="导入说明"
        description="请上传Excel文件，文件格式应包含：开发商名称、地点等字段。"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      />

      <a-upload-dragger
        v-model:fileList="fileList"
        :before-upload="beforeUpload"
        :remove="handleRemove"
        accept=".xlsx,.xls"
      >
        <p class="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持单个文件上传，仅支持 .xlsx 和 .xls 格式文件
        </p>
      </a-upload-dragger>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { InboxOutlined } from '@ant-design/icons-vue';
  import { developerApi } from '/@/api/asset/developer-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emit = defineEmits(['refresh']);

  // 模态框状态
  const visible = ref(false);
  const confirmLoading = ref(false);

  // 文件列表
  const fileList = ref([]);

  /**
   * 显示模态框
   */
  function showModal() {
    visible.value = true;
    fileList.value = [];
  }

  /**
   * 上传前检查
   */
  function beforeUpload(file) {
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   file.type === 'application/vnd.ms-excel';
    
    if (!isExcel) {
      message.error('只能上传Excel文件！');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB！');
      return false;
    }

    // 阻止自动上传
    return false;
  }

  /**
   * 移除文件
   */
  function handleRemove(file) {
    const index = fileList.value.indexOf(file);
    const newFileList = fileList.value.slice();
    newFileList.splice(index, 1);
    fileList.value = newFileList;
  }

  /**
   * 确认导入
   */
  async function handleOk() {
    if (fileList.value.length === 0) {
      message.warning('请选择要导入的文件');
      return;
    }

    try {
      confirmLoading.value = true;
      const file = fileList.value[0];
      await developerApi.importDevelopers(file);
      message.success('导入成功');
      visible.value = false;
      emit('refresh');
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      confirmLoading.value = false;
    }
  }

  /**
   * 取消导入
   */
  function handleCancel() {
    visible.value = false;
    fileList.value = [];
  }

  // 暴露方法
  defineExpose({
    showModal,
  });
</script>

<style scoped>
  .import-modal-content {
    padding: 16px 0;
  }
</style>