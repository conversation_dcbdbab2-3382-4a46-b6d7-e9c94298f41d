package cn.edu.xmut.soms.admin.module.system.developer.service;

import cn.edu.xmut.soms.admin.module.system.developer.dao.DeveloperContactDao;
import cn.edu.xmut.soms.admin.module.system.developer.dao.DeveloperDao;
import cn.edu.xmut.soms.admin.module.system.developer.domain.entity.DeveloperContactEntity;
import cn.edu.xmut.soms.admin.module.system.developer.domain.entity.DeveloperEntity;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperContactAddForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperContactQueryForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperContactUpdateForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.vo.DeveloperContactVO;
import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.common.util.SmartBeanUtil;
import cn.edu.xmut.soms.base.common.util.SmartPageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 开发商联系人管理 Service
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Service
public class DeveloperContactService {

    @Resource
    private DeveloperContactDao developerContactDao;

    @Resource
    private DeveloperDao developerDao;

    /**
     * 分页查询联系人列表
     *
     * @param queryForm 查询条件
     * @return 分页结果
     */
    public PageResult<DeveloperContactVO> queryPage(DeveloperContactQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<DeveloperContactVO> list = developerContactDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 根据开发商ID查询联系人列表
     *
     * @param developerId 开发商ID
     * @return 联系人列表
     */
    public ResponseDTO<List<DeveloperContactVO>> getContactsByDeveloperId(Long developerId) {
        // 验证开发商是否存在
        DeveloperEntity developer = developerDao.selectById(developerId);
        if (developer == null) {
            return ResponseDTO.userErrorParam("开发商不存在");
        }

        List<DeveloperContactVO> contacts = developerContactDao.selectByDeveloperId(developerId);
        return ResponseDTO.ok(contacts);
    }

    /**
     * 根据ID查询联系人详情
     *
     * @param contactId 联系人ID
     * @return 联系人详情
     */
    public ResponseDTO<DeveloperContactVO> queryById(Long contactId) {
        DeveloperContactEntity contactEntity = developerContactDao.selectById(contactId);
        if (contactEntity == null) {
            return ResponseDTO.userErrorParam("联系人不存在");
        }

        DeveloperContactVO contactVO = SmartBeanUtil.copy(contactEntity, DeveloperContactVO.class);

        // 获取开发商名称
        DeveloperEntity developer = developerDao.selectById(contactEntity.getDeveloperId());
        if (developer != null) {
            contactVO.setDeveloperName(developer.getDeveloperName());
        }

        return ResponseDTO.ok(contactVO);
    }

    /**
     * 新增联系人
     *
     * @param addForm 新增表单
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> add(DeveloperContactAddForm addForm) {
        // 验证开发商是否存在
        DeveloperEntity developer = developerDao.selectById(addForm.getDeveloperId());
        if (developer == null) {
            return ResponseDTO.userErrorParam("开发商不存在");
        }

        DeveloperContactEntity contactEntity = SmartBeanUtil.copy(addForm, DeveloperContactEntity.class);
        developerContactDao.insert(contactEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新联系人
     *
     * @param updateForm 更新表单
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> update(DeveloperContactUpdateForm updateForm) {
        DeveloperContactEntity existingContact = developerContactDao.selectById(updateForm.getContactId());
        if (existingContact == null) {
            return ResponseDTO.userErrorParam("联系人不存在");
        }

        // 验证开发商是否存在
        DeveloperEntity developer = developerDao.selectById(updateForm.getDeveloperId());
        if (developer == null) {
            return ResponseDTO.userErrorParam("开发商不存在");
        }

        DeveloperContactEntity contactEntity = SmartBeanUtil.copy(updateForm, DeveloperContactEntity.class);
        developerContactDao.updateById(contactEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除联系人
     *
     * @param contactId 联系人ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> delete(Long contactId) {
        DeveloperContactEntity contact = developerContactDao.selectById(contactId);
        if (contact == null) {
            return ResponseDTO.userErrorParam("联系人不存在");
        }

        developerContactDao.deleteById(contactId);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除联系人
     *
     * @param contactIds 联系人ID列表
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> batchDelete(List<Long> contactIds) {
        if (contactIds == null || contactIds.isEmpty()) {
            return ResponseDTO.userErrorParam("请选择要删除的联系人");
        }

        developerContactDao.deleteBatchIds(contactIds);
        return ResponseDTO.ok();
    }

}
