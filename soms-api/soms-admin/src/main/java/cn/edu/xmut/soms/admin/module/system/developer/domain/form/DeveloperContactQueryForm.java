package cn.edu.xmut.soms.admin.module.system.developer.domain.form;

import cn.edu.xmut.soms.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开发商联系人查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "开发商联系人查询表单")
public class DeveloperContactQueryForm extends PageParam {

    @Schema(description = "开发商ID")
    private Long developerId;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "关键字")
    private String keyword;

}
