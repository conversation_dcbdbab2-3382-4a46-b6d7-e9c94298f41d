package cn.edu.xmut.soms.admin.module.system.developer.dao;

import cn.edu.xmut.soms.admin.module.system.developer.domain.entity.DeveloperContactEntity;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperContactQueryForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.vo.DeveloperContactVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 开发商联系人信息 DAO
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Mapper
public interface DeveloperContactDao extends BaseMapper<DeveloperContactEntity> {

    /**
     * 分页查询联系人列表
     *
     * @param page      分页参数
     * @param queryForm 查询条件
     * @return 联系人列表
     */
    List<DeveloperContactVO> queryPage(@Param("page") Page page, @Param("queryForm") DeveloperContactQueryForm queryForm);

    /**
     * 根据开发商ID查询联系人列表
     *
     * @param developerId 开发商ID
     * @return 联系人列表
     */
    List<DeveloperContactVO> selectByDeveloperId(@Param("developerId") Long developerId);

    /**
     * 根据开发商ID统计联系人数量
     *
     * @param developerId 开发商ID
     * @return 联系人数量
     */
    Integer countByDeveloperId(@Param("developerId") Long developerId);

    /**
     * 根据开发商ID删除所有联系人
     *
     * @param developerId 开发商ID
     * @return 删除的记录数
     */
    Integer deleteByDeveloperId(@Param("developerId") Long developerId);

}
