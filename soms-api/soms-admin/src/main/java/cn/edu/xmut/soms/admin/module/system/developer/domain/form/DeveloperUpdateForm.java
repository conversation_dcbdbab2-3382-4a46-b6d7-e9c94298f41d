package cn.edu.xmut.soms.admin.module.system.developer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 开发商更新表单
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Data
@Schema(description = "开发商更新表单")
public class DeveloperUpdateForm {

    @Schema(description = "开发商ID")
    @NotNull(message = "开发商ID不能为空")
    private Long developerId;

    @Schema(description = "开发商名称")
    @NotBlank(message = "开发商名称不能为空")
    @Size(max = 100, message = "开发商名称长度不能超过100个字符")
    private String developerName;

    @Schema(description = "省份")
    @Size(max = 50, message = "省份长度不能超过50个字符")
    private String province;

    @Schema(description = "城市")
    @Size(max = 50, message = "城市长度不能超过50个字符")
    private String city;

    @Schema(description = "详细地址")
    @Size(max = 200, message = "详细地址长度不能超过200个字符")
    private String location;

}
