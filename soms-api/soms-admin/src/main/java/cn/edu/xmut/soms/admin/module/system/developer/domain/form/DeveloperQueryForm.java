package cn.edu.xmut.soms.admin.module.system.developer.domain.form;

import cn.edu.xmut.soms.base.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开发商查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "开发商查询表单")
public class DeveloperQueryForm extends PageParam {

    @Schema(description = "开发商名称")
    private String developerName;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "详细地址")
    private String location;

    @Schema(description = "关键字")
    private String keyword;

}
