package cn.edu.xmut.soms.admin.module.system.developer.service;

import cn.edu.xmut.soms.admin.module.system.developer.dao.DeveloperContactDao;
import cn.edu.xmut.soms.admin.module.system.developer.dao.DeveloperDao;
import cn.edu.xmut.soms.admin.module.system.developer.domain.entity.DeveloperEntity;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperAddForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperQueryForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperUpdateForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.vo.DeveloperContactVO;
import cn.edu.xmut.soms.admin.module.system.developer.domain.vo.DeveloperVO;
import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.common.util.SmartBeanUtil;
import cn.edu.xmut.soms.base.common.util.SmartPageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 开发商管理 Service
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Service
public class DeveloperService {

    @Resource
    private DeveloperDao developerDao;

    @Resource
    private DeveloperContactDao developerContactDao;

    /**
     * 分页查询开发商列表
     *
     * @param queryForm 查询条件
     * @return 分页结果
     */
    public PageResult<DeveloperVO> queryPage(DeveloperQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<DeveloperVO> list = developerDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 根据ID查询开发商详情
     *
     * @param developerId 开发商ID
     * @return 开发商详情
     */
    public ResponseDTO<DeveloperVO> queryById(Long developerId) {
        DeveloperVO developerVO = developerDao.selectByIdWithContacts(developerId);
        if (developerVO == null) {
            return ResponseDTO.userErrorParam("开发商不存在");
        }

        // 查询联系人列表
        List<DeveloperContactVO> contacts = developerContactDao.selectByDeveloperId(developerId);
        developerVO.setContacts(contacts);

        return ResponseDTO.ok(developerVO);
    }

    /**
     * 新增开发商
     *
     * @param addForm 新增表单
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> add(DeveloperAddForm addForm) {
        // 检查开发商名称是否已存在
        DeveloperEntity existingDeveloper = developerDao.selectByDeveloperName(addForm.getDeveloperName());
        if (existingDeveloper != null) {
            return ResponseDTO.userErrorParam("开发商名称已存在");
        }

        DeveloperEntity developerEntity = SmartBeanUtil.copy(addForm, DeveloperEntity.class);
        developerDao.insert(developerEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新开发商
     *
     * @param updateForm 更新表单
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> update(DeveloperUpdateForm updateForm) {
        DeveloperEntity existingDeveloper = developerDao.selectById(updateForm.getDeveloperId());
        if (existingDeveloper == null) {
            return ResponseDTO.userErrorParam("开发商不存在");
        }

        // 检查开发商名称是否与其他开发商重复
        DeveloperEntity duplicateDeveloper = developerDao.selectByDeveloperName(updateForm.getDeveloperName());
        if (duplicateDeveloper != null && !duplicateDeveloper.getDeveloperId().equals(updateForm.getDeveloperId())) {
            return ResponseDTO.userErrorParam("开发商名称已存在");
        }

        DeveloperEntity developerEntity = SmartBeanUtil.copy(updateForm, DeveloperEntity.class);
        developerDao.updateById(developerEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除开发商
     *
     * @param developerId 开发商ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> delete(Long developerId) {
        DeveloperEntity developer = developerDao.selectById(developerId);
        if (developer == null) {
            return ResponseDTO.userErrorParam("开发商不存在");
        }

        // 删除开发商（级联删除联系人由数据库外键约束处理）
        developerDao.deleteById(developerId);

        return ResponseDTO.ok();
    }

    /**
     * 批量删除开发商
     *
     * @param developerIds 开发商ID列表
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> batchDelete(List<Long> developerIds) {
        if (developerIds == null || developerIds.isEmpty()) {
            return ResponseDTO.userErrorParam("请选择要删除的开发商");
        }

        // 批量删除开发商
        developerDao.deleteBatchIds(developerIds);

        return ResponseDTO.ok();
    }

    /**
     * 获取所有开发商列表（用于下拉选择）
     *
     * @return 开发商列表
     */
    public ResponseDTO<List<DeveloperVO>> getAllDevelopers() {
        List<DeveloperVO> developers = developerDao.selectAll();
        return ResponseDTO.ok(developers);
    }

    /**
     * 获取导出数据
     *
     * @param queryForm 查询条件
     * @return 导出数据
     */
    public ResponseDTO<List<DeveloperVO>> getExportData(DeveloperQueryForm queryForm) {
        // 设置不分页，查询所有数据
        queryForm.setPageSize(Long.MAX_VALUE);
        queryForm.setPageNum(1L);

        PageResult<DeveloperVO> pageResult = queryPage(queryForm);
        return ResponseDTO.ok(pageResult.getList());
    }

    /**
     * 导入开发商数据
     *
     * @param file 导入文件
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Void> importDevelopers(MultipartFile file) {
        // TODO: 实现Excel导入功能
        // 1. 解析Excel文件
        // 2. 验证数据格式
        // 3. 批量插入数据
        // 4. 返回导入结果

        return ResponseDTO.userErrorParam("导入功能暂未实现");
    }

}
