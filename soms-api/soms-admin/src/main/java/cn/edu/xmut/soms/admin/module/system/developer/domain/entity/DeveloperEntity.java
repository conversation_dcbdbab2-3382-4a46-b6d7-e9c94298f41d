package cn.edu.xmut.soms.admin.module.system.developer.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 开发商信息表 实体类
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_developer")
public class DeveloperEntity {

    /**
     * 开发商ID
     */
    @TableId(type = IdType.AUTO)
    private Long developerId;

    /**
     * 开发商名称
     */
    private String developerName;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String location;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
