package cn.edu.xmut.soms.admin.module.system.developer.dao;

import cn.edu.xmut.soms.admin.module.system.developer.domain.entity.DeveloperEntity;
import cn.edu.xmut.soms.admin.module.system.developer.domain.form.DeveloperQueryForm;
import cn.edu.xmut.soms.admin.module.system.developer.domain.vo.DeveloperVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 开发商信息 DAO
 *
 * <AUTHOR>
 * @Date 2025-07-28
 */
@Mapper
public interface DeveloperDao extends BaseMapper<DeveloperEntity> {

    /**
     * 分页查询开发商列表
     *
     * @param page      分页参数
     * @param queryForm 查询条件
     * @return 开发商列表
     */
    List<DeveloperVO> queryPage(@Param("page") Page page, @Param("queryForm") DeveloperQueryForm queryForm);

    /**
     * 根据开发商名称查询开发商
     *
     * @param developerName 开发商名称
     * @return 开发商实体
     */
    DeveloperEntity selectByDeveloperName(@Param("developerName") String developerName);

    /**
     * 查询所有开发商列表（用于下拉选择）
     *
     * @return 开发商列表
     */
    List<DeveloperVO> selectAll();

    /**
     * 根据ID查询开发商详情（包含联系人信息）
     *
     * @param developerId 开发商ID
     * @return 开发商详情
     */
    DeveloperVO selectByIdWithContacts(@Param("developerId") Long developerId);

}
