package cn.edu.xmut.soms.base.config;

import cn.edu.xmut.soms.base.common.util.SmartStringUtil;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson 配置类
 * 复用 Spring Data Redis 配置，避免重复配置
 *
 * <AUTHOR>
 * @Date 2025-07-29
 */
@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.host:127.0.0.1}")
    private String host;

    @Value("${spring.data.redis.port:6379}")
    private int port;

    @Value("${spring.data.redis.password:}")
    private String password;

    @Value("${spring.data.redis.database:0}")
    private int database;

    @Value("${spring.data.redis.timeout:10000ms}")
    private String timeoutStr;

    @Value("${spring.data.redis.lettuce.pool.max-active:8}")
    private int maxActive;

    @Value("${spring.data.redis.lettuce.pool.max-wait:60000ms}")
    private String maxWaitStr;

    @Bean
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        Config config = new Config();
        
        // 解析超时时间（去掉 ms 后缀）
        int timeout = parseTimeout(timeoutStr);
        int maxWait = parseTimeout(maxWaitStr);
        
        // 单服务器配置，复用 Spring Data Redis 的配置
        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setPassword(SmartStringUtil.isEmpty(password) ? null : password)
                .setDatabase(database)
                // 连接池配置（基于 Spring Data Redis 配置）
                .setConnectionPoolSize(maxActive)
                .setConnectionMinimumIdleSize(Math.max(2, maxActive / 4))
                // 超时配置（基于 Spring Data Redis 配置）
                .setConnectTimeout(timeout)
                .setTimeout(timeout)
                .setIdleConnectionTimeout(Math.max(300000, maxWait * 5))
                // 重试配置
                .setRetryAttempts(3)
                .setRetryInterval(3000)
                // 心跳检测（超时时间的一半）
                .setPingConnectionInterval(Math.max(30000, timeout / 2))
                .setKeepAlive(true)
                // 连接监控
                .setTcpNoDelay(true)
                .setDnsMonitoringInterval(5000);

        // 线程池配置
        config.setThreads(4);
        config.setNettyThreads(8);

        return Redisson.create(config);
    }

    /**
     * 解析超时时间字符串，去掉 ms 后缀
     */
    private int parseTimeout(String timeoutStr) {
        if (SmartStringUtil.isEmpty(timeoutStr)) {
            return 10000; // 默认 10 秒
        }
        
        String cleanStr = timeoutStr.toLowerCase().replace("ms", "").trim();
        try {
            return Integer.parseInt(cleanStr);
        } catch (NumberFormatException e) {
            return 10000; // 解析失败时使用默认值
        }
    }
}